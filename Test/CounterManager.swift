//
//  CounterManager.swift
//  Test
//
//  Created by gondo on 16/09/2025.
//

import Foundation
import WidgetKit
internal import Combine

class CounterManager: ObservableObject {
    static let shared = CounterManager()
    
    // App Group identifier - you'll need to configure this in your project settings
    private let appGroupIdentifier = "group.ios26-test.testing-widget-gallery.shared"
    private let counterKey = "shared_counter"
    
    @Published var count: Int {
        didSet {
            saveCount()
            // Reload widgets when count changes
            WidgetCenter.shared.reloadAllTimelines()
        }
    }
    
    private init() {
        // Initialize count first, then load the saved value
        self.count = 0
        self.count = loadCount()
    }
    
    private func loadCount() -> Int {
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else {
            print("Failed to create UserDefaults with app group")
            return 0
        }
        return userDefaults.integer(forKey: counterKey)
    }
    
    private func saveCount() {
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else {
            print("Failed to create UserDefaults with app group")
            return
        }
        userDefaults.set(count, forKey: counterKey)
    }
    
    func increment() {
        count += 1
    }
    
    func getCurrentCount() -> Int {
        return loadCount()
    }
}
