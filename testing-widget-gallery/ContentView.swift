//
//  ContentView.swift
//  testing-widget-gallery
//
//  Created by gondo on 16/09/2025.
//

import SwiftUI

struct ContentView: View {
    @StateObject private var counterManager = CounterManager.shared

    var body: some View {
        VStack(spacing: 30) {
            Text("Counter App")
                .font(.largeTitle)
                .fontWeight(.bold)

            Text("\(counterManager.count)")
                .font(.system(size: 72, weight: .bold, design: .rounded))
                .foregroundColor(.blue)

            Button(action: {
                counterManager.increment()
            }) {
                Text("Increment")
                    .font(.title2)
                    .fontWeight(.semibold)
                    .foregroundColor(.white)
                    .frame(width: 200, height: 60)
                    .background(Color.blue)
                    .cornerRadius(15)
            }
        }
        .padding()
    }
}

#Preview {
    ContentView()
}
